package com.ruoyi.web.controller.merchant;

import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.service.IMerchantService;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.common.core.domain.entity.SysRole;

/**
 * 商家登录验证
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/merchant")
public class MerchantLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IMerchantService merchantService;

    /**
     * 商家登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @Anonymous
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();

        // 直接使用ruoyi的登录验证服务
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);

        // 获取当前登录用户的商家信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            Long merchantId = getMerchantIdFromUser(loginUser.getUser());
            if (merchantId != null) {
                Merchant merchant = merchantService.selectMerchantById(merchantId);
                if (merchant != null) {
                    ajax.put("merchantId", merchant.getMerchantId());
                    ajax.put("merchantName", merchant.getMerchantName());
                    ajax.put("merchantCode", merchant.getMerchantCode());
                }
            }
        }

        return ajax;
    }

    /**
     * 获取商家用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null)
        {
            return AjaxResult.error("用户信息获取失败");
        }

        SysUser user = loginUser.getUser();
        Long merchantId = getMerchantIdFromUser(user);
        Merchant merchant = null;
        if (merchantId != null) {
            merchant = merchantService.selectMerchantById(merchantId);
        }

        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);

        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("merchant", merchant);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取商家路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("/getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    /**
     * 商家退出登录
     */
    @PostMapping("/logout")
    public AjaxResult logout()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null)
        {
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            // AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginUser.getUsername(), Constants.LOGOUT, "退出成功"));
        }
        return AjaxResult.success();
    }

    /**
     * 从系统用户中获取商家ID
     *
     * @param user 系统用户
     * @return 商家ID
     */
    private Long getMerchantIdFromUser(SysUser user)
    {
        if (user == null) {
            return null;
        }

        // 方法1：从用户备注中获取商家ID（格式：merchantId:123）
        if (user.getRemark() != null && user.getRemark().startsWith("merchantId:")) {
            try {
                return Long.parseLong(user.getRemark().substring("merchantId:".length()));
            } catch (NumberFormatException e) {
                // 忽略解析错误
            }
        }

        // 方法2：通过用户角色判断（如果有商家角色，可以设置默认商家ID）
        if (user.getRoles() != null) {
            for (SysRole role : user.getRoles()) {
                if ("merchant_admin".equals(role.getRoleKey())) {
                    // 这里可以根据实际业务逻辑返回对应的商家ID
                    // 暂时返回用户ID作为商家ID（需要根据实际情况调整）
                    return user.getUserId();
                }
            }
        }

        return null;
    }
}
