import request from '@/utils/request'

// 查询商家列表
export function listMerchant(query) {
  return request({
    url: '/system/merchant/list',
    method: 'get',
    params: query
  })
}

// 查询商家详细
export function getMerchant(merchantId) {
  return request({
    url: '/system/merchant/' + merchantId,
    method: 'get'
  })
}

// 新增商家
export function addMerchant(data) {
  return request({
    url: '/system/merchant',
    method: 'post',
    data: data
  })
}

// 修改商家
export function updateMerchant(data) {
  return request({
    url: '/system/merchant',
    method: 'put',
    data: data
  })
}

// 删除商家
export function delMerchant(merchantId) {
  return request({
    url: '/system/merchant/' + merchantId,
    method: 'delete'
  })
}

// 状态修改
export function changeStatus(merchantId, status) {
  const data = {
    merchantId,
    status
  }
  return request({
    url: '/system/merchant/changeStatus',
    method: 'put',
    data: data
  })
}

// 校验商家编码
export function checkMerchantCodeUnique(merchant) {
  return request({
    url: '/system/merchant/checkMerchantCodeUnique',
    method: 'post',
    data: merchant
  })
}

// 查询即将到期的商家
export function getExpiringSoon(days) {
  return request({
    url: '/system/merchant/expiring/' + days,
    method: 'get'
  })
}

// 查询已过期的商家
export function getExpiredMerchants() {
  return request({
    url: '/system/merchant/expired',
    method: 'get'
  })
}

// 更新过期商家状态
export function updateExpiredStatus() {
  return request({
    url: '/system/merchant/updateExpiredStatus',
    method: 'put'
  })
}
