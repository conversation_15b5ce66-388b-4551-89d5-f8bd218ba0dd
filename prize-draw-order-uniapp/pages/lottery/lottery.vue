<template>
  <view class="lottery-container">
    <!-- 商家信息 -->
    <view class="merchant-header" v-if="merchantInfo">
      <view class="merchant-name">{{ merchantInfo.merchantName }}</view>
      <view class="activity-name" v-if="currentActivity">{{ currentActivity.activityName }}</view>
    </view>

    <!-- 抽奖结果展示（用户已抽过奖时显示） -->
    <view class="lottery-result-container" v-if="hasDrawn && lotteryResult">
      <view class="result-wrapper">
        <view class="result-icon">
          <text v-if="lotteryResult.isWinner === '1'">🎉</text>
          <text v-else>😊</text>
        </view>
        <view class="result-title">
          <text v-if="lotteryResult.isWinner === '1'">恭喜中奖！</text>
          <text v-else>谢谢参与</text>
        </view>
        <view class="result-prize">
          <text>{{ lotteryResult.prizeName }}</text>
        </view>
        <view class="result-time">
          <text>抽奖时间：{{ formatTime(lotteryResult.drawTime) }}</text>
        </view>
        <view class="result-status" v-if="lotteryResult.isWinner === '1'">
          <text :class="{ 'claimed': lotteryResult.claimStatus === '1' }">
            {{ lotteryResult.claimStatus === '1' ? '已领取' : '待领取' }}
          </text>
        </view>
      </view>
    </view>

    <!-- 抽奖转盘（用户未抽过奖时显示） -->
    <view class="lottery-wheel-container" v-if="!hasDrawn">
      <view class="wheel-wrapper">
        <canvas
          canvas-id="lotteryWheel"
          class="lottery-wheel"
          :style="{ transform: `rotate(${wheelRotation}deg)` }"
        ></canvas>
        <view class="wheel-pointer"></view>
        <view class="wheel-center" @click="startLottery">
          <text class="center-text">{{ isDrawing ? '抽奖中...' : '开始抽奖' }}</text>
        </view>
      </view>
    </view>

    <!-- 剩余次数 -->
    <view class="remaining-info" v-if="currentActivity && !hasDrawn">
      <text>剩余抽奖次数：{{ remainingDraws }}</text>
    </view>

    <!-- 奖品列表 -->
    <view class="prize-list" v-if="prizeList.length > 0">
      <view class="prize-title">奖品设置</view>
      <view class="prize-items">
        <view
          class="prize-item"
          v-for="(prize, index) in prizeList"
          :key="index"
        >
          <view class="prize-name">{{ prize.name }}</view>
          <view class="prize-probability">中奖率：{{ prize.probability }}%</view>
        </view>
      </view>
    </view>

    <!-- 抽奖记录 -->
    <view class="lottery-records">
      <view class="records-header">
        <text class="records-title">我的记录</text>
        <text class="view-all" @click="viewAllRecords">查看全部</text>
      </view>
      <view class="records-list" v-if="recentRecords.length > 0">
        <view
          class="record-item"
          v-for="record in recentRecords"
          :key="record.recordId"
        >
          <view class="record-info">
            <text class="record-prize">{{ record.prizeName }}</text>
            <text class="record-time">{{ formatTime(record.drawTime) }}</text>
          </view>
          <view class="record-status">
            <text
              class="status-text"
              :class="{ 'won': record.isWinner === '1', 'claimed': record.claimStatus === '1' }"
            >
              {{ getRecordStatusText(record) }}
            </text>
          </view>
        </view>
      </view>
      <view class="no-records" v-else>
        <text>暂无抽奖记录</text>
      </view>
    </view>
  </view>
</template>

<script>
import { merchantApi, lotteryApi } from '@/utils/api.js'

export default {
  data() {
    return {
      merchantCode: '',
      tableNumber: '',
      merchantInfo: null,
      currentActivity: null,
      prizeList: [],
      remainingDraws: 0,
      recentRecords: [],
      wheelRotation: 0,
      isDrawing: false,
      userOpenid: '',
      hasDrawn: false,
      lotteryResult: null
    }
  },

  onLoad(options) {
    this.merchantCode = options.merchantCode || 'demo001'
    this.tableNumber = options.tableNumber || '001'

    this.initPage()
  },

  methods: {
    async initPage() {
      try {
        // 获取用户openid（实际项目中需要通过微信登录获取）
        // 为了测试重复抽奖限制，这里使用固定的测试用户ID
        this.userOpenid = 'test_user_001'

        // 加载商家信息
        await this.loadMerchantInfo()

        // 加载当前活动
        await this.loadCurrentActivity()

        // 检查用户抽奖状态
        await this.checkUserLotteryStatus()

        // 加载用户记录
        await this.loadUserRecords()

        // 绘制转盘（只有在用户还没抽过奖时才绘制）
        if (!this.hasDrawn) {
          this.drawWheel()
        }

      } catch (error) {
        console.error('页面初始化失败:', error)
        this.handleError(error)
      }
    },

    async loadMerchantInfo() {
      try {
        const res = await merchantApi.getMerchantInfo(this.merchantCode)
        if (res.code === 200) {
          this.merchantInfo = res.data
        }
      } catch (error) {
        this.handleError(error)
      }
    },

    async loadCurrentActivity() {
      try {
        const res = await lotteryApi.getCurrentActivity(this.merchantCode)
        if (res.code === 200 && res.data) {
          this.currentActivity = res.data
          this.prizeList = JSON.parse(res.data.prizeConfig || '[]')

          // 获取剩余抽奖次数
          await this.loadRemainingDraws()
        }
      } catch (error) {
        console.error('获取活动信息失败:', error)
      }
    },

    async loadRemainingDraws() {
      if (!this.currentActivity) return

      try {
        const res = await lotteryApi.getRemainingDraws(this.currentActivity.activityId, this.userOpenid)
        if (res.code === 200) {
          this.remainingDraws = res.data
        }
      } catch (error) {
        console.error('获取剩余次数失败:', error)
      }
    },

    async loadUserRecords() {
      try {
        const res = await lotteryApi.getUserRecords(this.userOpenid)
        if (res.code === 200) {
          this.recentRecords = res.data.slice(0, 5) // 只显示最近5条
        }
      } catch (error) {
        console.error('获取用户记录失败:', error)
      }
    },

    async checkUserLotteryStatus() {
      if (!this.currentActivity) return

      try {
        const res = await lotteryApi.getUserLotteryStatus(this.currentActivity.activityId, this.userOpenid)
        if (res.code === 200) {
          const data = res.data
          this.hasDrawn = data.hasDrawn
          this.remainingDraws = data.remainingDraws

          if (data.hasDrawn && data.lotteryRecord) {
            this.lotteryResult = data.lotteryRecord
          }
        }
      } catch (error) {
        console.error('获取用户抽奖状态失败:', error)
      }
    },

    drawWheel() {
      if (this.prizeList.length === 0) return

      const ctx = uni.createCanvasContext('lotteryWheel', this)
      const centerX = 150
      const centerY = 150
      const radius = 140
      const anglePerPrize = 360 / this.prizeList.length

      this.prizeList.forEach((prize, index) => {
        const startAngle = (index * anglePerPrize - 90) * Math.PI / 180
        const endAngle = ((index + 1) * anglePerPrize - 90) * Math.PI / 180

        // 绘制扇形
        ctx.beginPath()
        ctx.moveTo(centerX, centerY)
        ctx.arc(centerX, centerY, radius, startAngle, endAngle)
        ctx.closePath()

        // 设置颜色
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
        ctx.setFillStyle(colors[index % colors.length])
        ctx.fill()

        // 绘制边框
        ctx.setStrokeStyle('#fff')
        ctx.setLineWidth(2)
        ctx.stroke()

        // 绘制文字
        ctx.save()
        ctx.translate(centerX, centerY)
        ctx.rotate((startAngle + endAngle) / 2)
        ctx.setFillStyle('#fff')
        ctx.setFontSize(14)
        ctx.setTextAlign('center')
        ctx.fillText(prize.name, radius * 0.7, 5)
        ctx.restore()
      })

      ctx.draw()
    },

    async startLottery() {
      if (this.isDrawing) return
      if (this.hasDrawn) {
        uni.showToast({
          title: '您已经抽过奖了',
          icon: 'none'
        })
        return
      }
      if (this.remainingDraws <= 0) {
        uni.showToast({
          title: '抽奖次数已用完',
          icon: 'none'
        })
        return
      }
      if (!this.currentActivity) {
        uni.showToast({
          title: '暂无可参与的活动',
          icon: 'none'
        })
        return
      }

      this.isDrawing = true

      try {
        const drawData = {
          activityId: this.currentActivity.activityId,
          userOpenid: this.userOpenid,
          userNickname: '用户' + this.userOpenid.slice(-4),
          userAvatar: '',
          tableId: null // 如果有桌台信息可以传入
        }

        const res = await lotteryApi.performDraw(drawData)
        if (res.code === 200) {
          const result = res.data

          // 计算转盘旋转角度
          const prizeIndex = this.prizeList.findIndex(p => p.name === result.prizeName)
          const targetAngle = prizeIndex >= 0 ?
            360 - (prizeIndex * (360 / this.prizeList.length)) + (360 / this.prizeList.length / 2) :
            360

          // 转盘动画
          const finalRotation = this.wheelRotation + 1800 + targetAngle // 转5圈后停在目标位置
          this.wheelRotation = finalRotation

          // 延迟显示结果
          setTimeout(() => {
            this.showResult(result)
            this.isDrawing = false
            this.hasDrawn = true
            this.lotteryResult = result
            this.remainingDraws = 0
            this.loadUserRecords() // 刷新记录
          }, 3000)

        } else {
          throw new Error(res.msg || '抽奖失败')
        }
      } catch (error) {
        this.isDrawing = false
        this.handleError(error)
      }
    },

    showResult(result) {
      const isWinner = result.isWinner === '1'
      const title = isWinner ? '恭喜中奖！' : '很遗憾'
      const content = isWinner ?
        `您获得了：${result.prizeName}` :
        '谢谢参与，再接再厉！'

      uni.showModal({
        title: title,
        content: content,
        showCancel: false,
        confirmText: isWinner ? '去领取' : '确定',
        success: (res) => {
          if (res.confirm && isWinner) {
            // 跳转到领取页面
            uni.navigateTo({
              url: `/pages/claim/claim?recordId=${result.recordId}`
            })
          }
        }
      })
    },

    viewAllRecords() {
      uni.navigateTo({
        url: `/pages/records/records?userOpenid=${this.userOpenid}`
      })
    },

    formatTime(timeStr) {
      const date = new Date(timeStr)
      return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
    },

    getRecordStatusText(record) {
      if (record.isWinner === '0') {
        return '未中奖'
      } else if (record.claimStatus === '1') {
        return '已领取'
      } else {
        return '待领取'
      }
    },

    handleError(error) {
      let message = error.message || error.msg || '系统异常'

      // 检查是否是商家到期错误
      if (message.includes('过期') || message.includes('到期')) {
        uni.showModal({
          title: '系统提示',
          content: message,
          showCancel: false,
          confirmText: '我知道了'
        })
      } else {
        uni.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.lottery-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
}

.merchant-header {
  text-align: center;
  margin-bottom: 60rpx;

  .merchant-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 10rpx;
  }

  .activity-name {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.lottery-result-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .result-wrapper {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    text-align: center;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
    min-width: 500rpx;

    .result-icon {
      font-size: 120rpx;
      margin-bottom: 30rpx;
    }

    .result-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }

    .result-prize {
      font-size: 32rpx;
      color: #ff4757;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .result-time {
      font-size: 24rpx;
      color: #666;
      margin-bottom: 20rpx;
    }

    .result-status {
      font-size: 28rpx;

      .claimed {
        color: #2ed573;
      }

      text:not(.claimed) {
        color: #ff6b6b;
      }
    }
  }
}

.lottery-wheel-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .wheel-wrapper {
    position: relative;
    width: 600rpx;
    height: 600rpx;
  }

  .lottery-wheel {
    width: 600rpx;
    height: 600rpx;
    border-radius: 50%;
    transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .wheel-pointer {
    position: absolute;
    top: -20rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 30rpx solid transparent;
    border-right: 30rpx solid transparent;
    border-bottom: 60rpx solid #ff4757;
    z-index: 10;
  }

  .wheel-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120rpx;
    height: 120rpx;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.3);
    z-index: 10;

    .center-text {
      font-size: 24rpx;
      font-weight: bold;
      color: #333;
      text-align: center;
    }
  }
}

.remaining-info {
  text-align: center;
  margin-bottom: 40rpx;

  text {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
    padding: 15rpx 30rpx;
    border-radius: 30rpx;
  }
}

.prize-list {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;

  .prize-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .prize-items {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }

  .prize-item {
    flex: 1;
    min-width: 200rpx;
    background: #f8f9fa;
    border-radius: 10rpx;
    padding: 20rpx;
    text-align: center;

    .prize-name {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 10rpx;
    }

    .prize-probability {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.lottery-records {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;

  .records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .records-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .view-all {
      font-size: 26rpx;
      color: #667eea;
    }
  }

  .records-list {
    .record-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .record-info {
        flex: 1;

        .record-prize {
          font-size: 28rpx;
          color: #333;
          display: block;
          margin-bottom: 5rpx;
        }

        .record-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .record-status {
        .status-text {
          font-size: 24rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          background: #f0f0f0;
          color: #666;

          &.won {
            background: #fff3cd;
            color: #856404;
          }

          &.claimed {
            background: #d4edda;
            color: #155724;
          }
        }
      }
    }
  }

  .no-records {
    text-align: center;
    padding: 60rpx 0;
    color: #999;
    font-size: 28rpx;
  }
}
</style>