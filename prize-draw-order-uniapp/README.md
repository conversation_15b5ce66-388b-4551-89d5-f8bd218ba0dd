# 扫码点餐抽奖系统 - UniApp小程序

## 项目简介

这是一个基于UniApp开发的扫码点餐抽奖系统小程序，用户可以通过扫描桌台二维码参与抽奖活动，并跳转到美团小程序进行点餐。

## 功能特性

- 🎯 **扫码识别** - 扫描桌台二维码获取商家和活动信息
- 🎰 **抽奖转盘** - 精美的转盘动画和抽奖逻辑
- 📱 **美团跳转** - 无缝跳转到美团小程序点餐
- 📋 **记录管理** - 用户抽奖记录和奖品管理
- 🎁 **奖品领取** - 完整的奖品领取流程
- 🎨 **精美UI** - 渐变色彩和动画效果

## 项目结构

```
prize-draw-order-uniapp/
├── pages/                  # 页面文件
│   ├── index/             # 首页 - 扫码入口
│   ├── lottery/           # 抽奖页面 - 转盘抽奖
│   ├── result/            # 结果页面 - 抽奖结果展示
│   ├── records/           # 记录页面 - 用户抽奖记录
│   └── claim/             # 领取页面 - 奖品领取
├── static/                # 静态资源
├── utils/                 # 工具类
├── App.vue               # 应用入口组件
├── main.js               # 主入口文件
├── pages.json            # 页面路由配置
├── manifest.json         # 应用配置文件
├── uni.scss              # 全局样式
└── package.json          # 项目依赖配置
```

## 开发环境

### 推荐方式：HBuilderX

1. **下载安装HBuilderX**
   - 访问：https://www.dcloud.io/hbuilderx.html
   - 下载并安装HBuilderX标准版（免费）

2. **导入项目**
   - 打开HBuilderX
   - 文件 → 导入 → 从本地目录导入
   - 选择项目根目录

3. **运行项目**
   - 右键项目根目录
   - 运行 → 运行到小程序模拟器 → 微信开发者工具

### 命令行方式

```bash
# 安装依赖
npm install

# 开发微信小程序
npm run dev:mp-weixin

# 构建微信小程序
npm run build:mp-weixin
```

## 配置说明

### 1. 小程序配置

在 `manifest.json` 中配置小程序AppID：

```json
{
  "mp-weixin": {
    "appid": "your-mini-program-appid"
  }
}
```

### 2. API接口配置

在 `utils/api.js` 中配置后端API地址。

## 页面说明

- **首页 (index)** - 扫码入口、商家信息、活动介绍
- **抽奖页面 (lottery)** - 转盘抽奖、奖品展示、抽奖规则
- **结果页面 (result)** - 中奖动画、奖品信息、领取说明
- **记录页面 (records)** - 抽奖历史、奖品状态、筛选功能
- **领取页面 (claim)** - 奖品详情、领取表单、确认领取

## 技术栈

- **框架**：UniApp
- **语言**：Vue.js 2.x + JavaScript
- **样式**：Sass/SCSS
- **平台**：微信小程序 + H5

## 支持平台

- ✅ 微信小程序
- ✅ H5
- ⚠️ 其他平台（需要适配）

---

**推荐使用HBuilderX进行开发！**
